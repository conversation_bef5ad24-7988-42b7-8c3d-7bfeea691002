package dto

import "bot/domain/service/bot/sdk"

type IdDTO struct {
	IDS []any `json:"ids"`
	ID  any   `json:"id"`
}

type CrudDTO[T any] struct {
	Insert  T   `json:"insert"`
	Update  T   `json:"update"`
	Delete  T   `json:"delete"`
	Select  T   `json:"select"`
	Inserts []T `json:"inserts"`
	Updates []T `json:"updates"`
	Selects []T `json:"selects"`
	Deletes []T `json:"deletes"`
}

type DeleteMsg struct {
	ConversationID string `json:"conversationID"`
	MessageID      string `json:"messageID"`
}

type BaseChat struct {
	// Id 消息id
	ID string `json:"id" form:"id"`

	// 消息内容
	Message string `json:"message" `

	// 图片数据 存储的是服务器 url 资源
	Images []string `json:"images"`

	// 消息角色
	Role string `json:"role" form:"role" `

	// 消息类型
	MessageType int `json:"messageType" form:"messageType" `

	// ReplyMsgId 回复的消息id
	ReplyMsgID string `json:"replyMsgID" form:"replyMsgID" `

	// conversationID 会话id
	ConversationID string `json:"conversationID" form:"conversationID" `

	// History 消息列表
	History []*HistoryDTO `form:"history" json:"history" `

	// Stream  是否流式响应
	Stream bool `form:"stream" json:"stream"`

	// PlatformID 平台id
	PluginID string `form:"pluginID" json:"pluginID"`
}

type ChatDTO struct {
	*sdk.BaseChat
}

type PaintingDTO struct {
	*sdk.BaseChat
	PaintingID        string  `json:"paintingID"`
	PromptTemplate    string  `json:"promptTemplate"`    // 提示词模版 一般用于特定场景的固定提示词
	NumInferenceSteps int     `json:"numInferenceSteps"` // 推理步数
	GuidanceScale     float64 `json:"guidanceScale"`     // 引导强度
	NegativePrompt    string  `json:"negativePrompt"`    // 负面提示词
	Prompt            string  `json:"prompt"`            // 提示词
	Size              string  `json:"size"`              // 尺寸
	Height            int     `json:"height"`            // 高度
	Width             int     `json:"width"`             // 宽度
	ImageCount        int     `json:"imageCount"`        // 每次绘画的图片数量
}

type SaveAgentDTO struct {
	ID      string `json:"id"`
	Content string `json:"content"`
}

type WriterDTO struct {
	*sdk.BaseChat
}

type HistoryDTO struct {
	Role    string   `json:"role" from:"role"` // one of ["system", "user", "assistant"]
	Content string   `json:"content"from:"content"`
	Images  [][]byte `json:"images,omitempty"from:"images"`
}

type ModelDTO struct {
	ID           string `json:"id"`
	PID          string `json:"pid"`
	UserID       string `json:"user_id"`
	Name         string `json:"name"`
	Model        string `json:"model"`
	Picture      string `json:"picture"`
	Size         string `json:"size"`
	IsDownload   bool   `json:"isDownload"`
	Digest       string `json:"digest"`
	ModelDetails string `json:"modelDetails"`
	CreateTime   string `json:"createTime"`
}

// PluginDTO LLM模型插件
type PluginDTO struct {
	ID         string `json:"id"`
	Name       string `json:"name"`  // 模型名称
	Code       string `json:"code"`  // 模型代码
	Icon       string `json:"icon"`  // 图标
	Model      string `json:"model"` // LLM模型
	PlatformID string `json:"platformID"`
	ConfigView string `json:"configView"` // 窗口
	Props      string `json:"props"`      // 属性
	Status     bool   `json:"status"`     // 状态
	CreateTime string `json:"createTime"` // 创建时间
}

type ConversationDTO struct {
	ID       string `form:"id" json:"id,omitempty"`
	Picture  string `form:"picture" json:"picture,omitempty"`
	Title    string `form:"title" json:"title,omitempty"`
	LastMsg  string `form:"lastMsg" json:"lastMsg,omitempty"`
	UpdateAt string `form:"updateAt" json:"updateAt,omitempty"`
}

type PlatformDTO struct {
	ID              string `json:"id"`
	Name            string `json:"name"`
	Icon            string `json:"icon"`
	Type            string `json:"type"`
	Config          string `json:"config"`
	ConfigView      string `json:"configView"`
	QuickConfigView string `json:"quickConfigView"`
	Current         bool   `json:"current"`
}

type PlatformModelQueryDTO struct {
	*PlatformDTO
	Model string `json:"model"` // 模型类型
}

type MessageDTO struct {
	ID             string `form:"id"json:"id,omitempty"`
	ConversationID string `form:"conversationID" json:"conversationID"`
	Picture        string `form:"picture" json:"picture"`
	ReplyMsgID     string `form:"replyMsgID" json:"replyMsgID"`
	Role           string `form:"role" json:"role"`
	MessageType    string `form:"messageType" json:"messageType"`
	Ex             string `form:"ex"json:"ex"`
	Content        string `form:"content"json:"content"`
}
