package dto

import "bot/domain/entity"

func NewConversationEntity(dto *ConversationDTO) *entity.ConversationEntity {
	return &entity.ConversationEntity{
		ID:       dto.ID,
		Picture:  dto.Picture,
		Title:    dto.Title,
		LastMsg:  dto.LastMsg,
		UpdateAt: dto.UpdateAt,
	}
}

func NewHistoryEntity(dto *HistoryDTO) *entity.HistoryEntity {
	return &entity.HistoryEntity{
		Role:    dto.Role,
		Content: dto.Content,
		Images:  dto.Images, // TODO: 处理 images
	}
}
func NewChatEntity(dto *ChatDTO) *entity.ChatEntity {
	data := &entity.ChatEntity{
		ID:             dto.ID,
		ConversationID: dto.ConversationID,
		ReplyMsgID:     dto.ReplyMsgID,
		Stream:         dto.BaseChat.Stream,
		PluginID:       dto.PluginID,
		Message:        dto.BaseChat.Message,
		History:        dto.History,
		Images:         dto.BaseChat.Images,
		Role:           dto.BaseChat.Role,
		MessageType:    dto.BaseChat.MessageType,
		WebSearch:      dto.BaseChat.WebSearch,
		Thinking:       dto.BaseChat.Thinking,
		PlatformID:     dto.BaseChat.PlatformID,
		Mcp:            dto.BaseChat.Mcp,
	}
	return data
}

func NewPaintingEntity(dto *PaintingDTO) *entity.PaintingEntity {
	return &entity.PaintingEntity{
		ChatEntity: &entity.ChatEntity{
			ID:             dto.ID,
			ConversationID: dto.ConversationID,
			ReplyMsgID:     dto.ReplyMsgID,
			Stream:         dto.BaseChat.Stream,
			PluginID:       dto.PluginID,
			Message:        dto.BaseChat.Message,
			History:        dto.History,
			Images:         dto.BaseChat.Images,
			Role:           dto.BaseChat.Role,
			MessageType:    dto.BaseChat.MessageType,
		},
		PaintingID:        dto.PaintingID,
		PromptTemplate:    dto.PromptTemplate,
		NumInferenceSteps: dto.NumInferenceSteps,
		GuidanceScale:     dto.GuidanceScale,
		NegativePrompt:    dto.NegativePrompt,
		Prompt:            dto.Prompt,
		Size:              dto.Size,
		Height:            dto.Height,
		Width:             dto.Width,
		ImageCount:        dto.ImageCount,
	}
}

func NewWriterEntity(dto *WriterDTO) *entity.WriterEntity {
	data := &entity.WriterEntity{
		ChatEntity: &entity.ChatEntity{
			ID:             dto.ID,
			ConversationID: dto.ConversationID,
			ReplyMsgID:     dto.ReplyMsgID,
			Stream:         dto.BaseChat.Stream,
			PluginID:       dto.PluginID,
			Message:        dto.BaseChat.Message,
			History:        dto.History,
			Images:         dto.BaseChat.Images,
			Role:           dto.BaseChat.Role,
			MessageType:    dto.BaseChat.MessageType,
		},
	}
	return data
}

func NewPluginEntity(data *PluginDTO) *entity.PluginEntity {
	return &entity.PluginEntity{
		ID:         data.ID,
		Name:       data.Name,
		Icon:       data.Icon,
		Code:       data.Code,
		PlatformID: data.PlatformID,
		Props:      data.Props,
		ConfigView: data.ConfigView,
		Status:     data.Status,
	}
}

func NewPlatformEntity(data *PlatformDTO) *entity.PlatformEntity {
	return &entity.PlatformEntity{
		ID:              data.ID,
		Name:            data.Name,
		Icon:            data.Icon,
		Type:            data.Type,
		Config:          data.Config,
		ConfigView:      data.ConfigView,
		QuickConfigView: data.QuickConfigView,
		Current:         data.Current,
	}
}

func NewPlatformsEntity(list []*PlatformDTO) []*entity.PlatformEntity {
	entities := make([]*entity.PlatformEntity, len(list))
	for i, data := range list {
		entities[i] = NewPlatformEntity(data)
	}
	return entities
}

func NewMessageEntity(data *MessageDTO) *entity.MessageEntity {
	return &entity.MessageEntity{
		ID:          data.ID,
		Content:     data.Content,
		MessageType: data.MessageType,
		Role:        data.Role,
		Ex:          data.Ex,
		ReplyMsgID:  data.ReplyMsgID,
	}
}
