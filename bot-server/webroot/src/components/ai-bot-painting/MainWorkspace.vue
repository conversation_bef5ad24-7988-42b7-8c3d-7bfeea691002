<template>
  <div class="main-workspace  flex flex-col size-full">
    <div class="workspace-content flex-1 size-full">
      <!-- 初始状态：绘画输入在中间 -->
      <div v-if="!isGenerating && !hasGeneratedImage && !hasCurrentData" class="initial-state">
        <div class="initial-content">
          <div class="input-area">
            <PromptInput :prompt-text="promptText" :is-generating="isGenerating" :has-generated-image="false"
              @update-prompt="updatePrompt" @generate-image="generateImage" />
          </div>
        </div>
      </div>

      <!-- 生成状态：显示轮播图和输入区域 -->
      <div v-else class="generation-state">
        <!-- 生成结果展示区域 -->
        <div class="result-display-area">
          <div class="result-container">
            <div class="result-image-area">
              <!-- 始终显示轮播图，让轮播图内部处理loading状态 -->
              <div class="image-container">
                <ImageCarousel
                  @save-images="handleSaveImages"
                  @download-single-image="handleDownloadImage"
                  @update-has-generated-image="handleUpdateHasGeneratedImage" />
              </div>
            </div>
          </div>
        </div>

        <!-- 输入控制区域 -->
        <PromptInput :prompt-text="promptText" :is-generating="isGenerating"
          :has-generated-image="hasGeneratedImage || isGenerating" @update-prompt="updatePrompt"
          @generate-image="generateImage" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import ImageCarousel from './ImageCarousel.vue';
import PromptInput from './PromptInput.vue';
import { useAiPaintingStore } from '../store/painting';
// import { PaintingMessage } from '../common/model/painting';

// 获取状态管理器
const aiPaintingStore = useAiPaintingStore();

// Props
interface Props {
  hasGeneratedImage: boolean;
  generatedImages: string[];
  lastPrompt: string;
  currentImageIndex: number;
  promptText: string;
}

const _props = withDefaults(defineProps<Props>(), {
  hasGeneratedImage: false,
  generatedImages: () => [],
  lastPrompt: '',
  currentImageIndex: 0,
  promptText: ''
});

// 从状态管理器获取生成状态
const isGenerating = computed(() => aiPaintingStore.ui.loading);

// 检查是否有当前数据
const hasCurrentData = computed(() => {
  const current = aiPaintingStore.current;
  return current && (
    (current.imageUrls && current.imageUrls.trim()) ||
    (current.prompt && current.prompt.trim()) ||
    (current.id && current.id.trim())
  );
});

// 保存图片数据接口
interface SaveImageData {
  folderName: string;
  saveCurrentOnly: boolean;
  saveAllImages: boolean;
  currentIndex: number;
  images: string[];
  prompt: string;
}

// Emits
const emit = defineEmits<{
  'download-image': [];
  'save-to-files': [];
  'clear-result': [];
  'update-current-index': [index: number];
  'previous-image': [];
  'next-image': [];
  'go-to-image': [index: number];
  'update-prompt': [text: string];
  'generate-image': [];
  'save-images': [data: SaveImageData];
  'download-single-image': [index: number];
  'update-has-generated-image': [value: boolean];
}>();

// Methods
const downloadImage = () => {
  emit('download-image');
};

const saveToFiles = () => {
  emit('save-to-files');
};

const clearResult = () => {
  emit('clear-result');
};

const updateCurrentIndex = (index: number) => {
  emit('update-current-index', index);
};

// 这些方法现在由 ImageCarousel 内部处理
// const previousImage = () => {
//   emit('previous-image');
// };

// const nextImage = () => {
//   emit('next-image');
// };

// const goToImage = (index: number) => {
//   emit('go-to-image', index);
// };

const updatePrompt = (text: string) => {
  emit('update-prompt', text);
};

const generateImage = () => {
  emit('generate-image');
};

// 处理新的保存事件
const handleSaveImages = (data: SaveImageData) => {
  emit('save-images', data);
};

const handleDownloadImage = (index: number) => {
  emit('download-single-image', index);
};

const handleUpdateHasGeneratedImage = (value: boolean) => {
  // 将状态变化传递给父组件
  console.log('MainWorkspace: hasGeneratedImage 更新为:', value);
  emit('update-has-generated-image', value);
};
</script>

<style scoped>
/* 主工作区 */
.main-workspace {
  background: var(--ant-color-bg-layout);
}

.workspace-content {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

/* 初始状态 */
.initial-state {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--ant-color-bg-layout);
  padding: 24px;
  min-height: 400px;
  max-height: calc(100vh - 240px);
}

.initial-content {
  width: 100%;
  max-width: 800px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 40px;
  background: var(--ant-color-bg-container);
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid var(--ant-color-border);
}

.input-area {
  width: 100%;
  max-width: 600px;
}

/* 生成状态 */
.generation-state {
  flex: 1;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

/* 生成结果展示区域 */
.result-display-area {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--ant-color-bg-layout);
  border-bottom: 1px solid var(--ant-color-border);
  overflow: hidden;
  padding: 24px;
  min-height: 400px;
  max-height: calc(100vh - 240px);
}

.result-container {
  width: 100%;
  max-width: 1400px;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.result-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--ant-color-text);
}

.result-actions {
  display: flex;
  gap: 8px;
}

.result-image-area {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--ant-color-bg-container);
  border: 1px solid var(--ant-color-border-secondary);
  border-radius: 12px;
  overflow: visible;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
}

.image-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: visible;
}

.loading-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 200px;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  color: var(--ant-color-primary);
}

.loading-spinner {
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid var(--ant-color-primary);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: 16px;
  font-weight: 500;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.empty-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 200px;
}

.empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  color: var(--ant-color-text-secondary);
}

.empty-icon {
  font-size: 48px;
  opacity: 0.6;
}

.empty-text {
  font-size: 16px;
  font-weight: 500;
}

.generating-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 200px;
}

.generating-animation {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  color: var(--ant-color-primary);
}

.generating-icon {
  font-size: 48px;
  animation: pulse 2s ease-in-out infinite;
}

.generating-text {
  font-size: 16px;
  font-weight: 500;
}

@keyframes pulse {

  0%,
  100% {
    transform: scale(1);
    opacity: 0.8;
  }

  50% {
    transform: scale(1.1);
    opacity: 1;
  }
}

/* 响应式调整 */
@media (max-width: 768px) {
  .initial-state {
    padding: 16px;
    min-height: 400px;
    max-height: calc(100vh - 180px);
  }

  .initial-content {
    max-width: 100%;
    padding: 24px;
  }

  .input-area {
    max-width: 100%;
  }

  .result-display-area {
    padding: 16px;
    min-height: 400px;
    max-height: calc(100vh - 180px);
  }

  .result-container {
    max-width: 100%;
  }

  .result-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .result-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .result-image-area {
    min-height: 280px;
  }
}

@media (max-width: 480px) {
  .initial-state {
    padding: 12px;
    min-height: 350px;
    max-height: calc(100vh - 160px);
  }

  .initial-content {
    max-width: 100%;
    padding: 20px;
  }

  .input-area {
    max-width: 100%;
  }

  .result-display-area {
    padding: 12px;
    min-height: 350px;
    max-height: calc(100vh - 160px);
  }

  .result-image-area {
    min-height: 220px;
  }
}
</style>