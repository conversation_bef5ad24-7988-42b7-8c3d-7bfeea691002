<template>
  <div class="adaptive-carousel size-full flex flex-col">
    <!-- 主轮播区域 -->
    <div class="carousel-container relative" :style="containerStyle">
      <!-- Loading 效果 - 当正在生成且没有图片时显示 -->
      <div v-if="isGenerating && images.length === 0" class="generating-overlay">
        <div class="generating-content">
          <div class="generating-spinner"></div>
          <span class="generating-text">正在生成图片...</span>
        </div>
      </div>

      <!-- 左侧箭头 -->
      <div class="carousel-arrow carousel-arrow-left" @click="prevImage">
        <span class="arrow-icon">&#10094;</span>
      </div>

      <!-- 轮播图主区域 -->
      <div class="carousel-main" 
           @touchstart="handleTouchStart" 
           @touchend="handleTouchEnd">
        <div class="carousel-track" ref="carouselTrack">
          <div v-for="(image, index) in images" :key="`${image}-${index}`" class="carousel-slide"
            :class="getSlideClass(index)" :style="getSlideStyle(index)" @click="goToImage(index)">
            <img class="carousel-image" :src="image" @load="onImageLoad($event, index)"
              @error="onImageError($event, index)" />
          </div>
        </div>
        
        <!-- 当有图片但仍在生成时显示小的loading指示器 -->
        <div v-if="isGenerating && images.length > 0" class="generating-indicator">
          <div class="generating-spinner-small"></div>
          <span class="generating-text-small">生成中...</span>
        </div>
      </div>

      <!-- 右侧箭头 -->
      <div class="carousel-arrow carousel-arrow-right" @click="nextImage">
        <span class="arrow-icon">&#10095;</span>
      </div>
    </div>

    <!-- 缩略图导航 - 优化版本 -->
    <div class="thumbnail-container" v-if="showThumbnails && images.length > 1">
      <div class="thumbnail-scroll-container">
        <div class="thumbnail-nav" ref="thumbnailNav">
          <div v-for="(image, index) in images" :key="index" class="thumbnail-item"
            :class="{ active: currentIndex === index }" @click="goToImage(index)">
            <div class="thumbnail-wrapper">
              <img :src="image" class="thumbnail-image" 
                   @load="onThumbnailLoad($event, index)" 
                   @error="onThumbnailError($event, index)" />
              <div class="thumbnail-overlay">
                <span class="thumbnail-index">{{ index + 1 }}</span>
              </div>
              <!-- 加载状态指示器 -->
              <div class="thumbnail-loading" v-if="thumbnailLoadingStates[index]">
                <div class="loading-spinner-small"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 缩略图滚动指示器 -->
      <div class="thumbnail-scroll-indicator" v-if="showScrollIndicator">
        <div class="scroll-dots">
          <div v-for="(_, index) in scrollPages" :key="index" 
               class="scroll-dot" :class="{ active: currentScrollPage === index }"
               @click="scrollToPage(index)">
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch, nextTick, onMounted, onUnmounted } from 'vue';
import { PaintingMessage } from '../common/model/painting';

// Props
interface Props {
  images?: string[];
  currentIndex?: number;
  showThumbnails?: boolean;
  maxWidth?: number;
  maxHeight?: number;
  aspectRatio?: 'auto' | 'square' | '4:3' | '16:9' | 'portrait';
  isGenerating?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  images: () => [],
  currentIndex: 0,
  showThumbnails: true,
  maxWidth: 500,
  maxHeight: 600,
  aspectRatio: 'auto',
  isGenerating: false
});

// Emits
const emit = defineEmits<{
  'update:currentIndex': [index: number];
  'imageLoad': [index: number, dimensions: { width: number; height: number }];
}>()

// 内部状态
const isTransitioning = ref(false);
const imageDimensions = ref<Map<number, { width: number; height: number; ratio: number }>>(new Map());
const containerDimensions = ref({ width: 400, height: 500 });
const thumbnailContainer = ref<HTMLElement>();
const thumbnailNav = ref<HTMLElement>();
const currentScrollPage = ref(0);
const scrollPages = ref<number[]>([]);
const thumbnailLoadingStates = ref<Map<number, boolean>>(new Map());

// 当前索引的响应式处理
const currentIndex = computed({
  get: () => props.currentIndex,
  set: (value: number) => {
    emit('update:currentIndex', value);
  }
});

// 计算是否需要显示滚动指示器
const showScrollIndicator = computed(() => {
  if (!thumbnailNav.value) return false;
  return thumbnailNav.value.scrollWidth > thumbnailNav.value.clientWidth;
});

// 计算滚动页数
const calculateScrollPages = () => {
  if (!thumbnailNav.value) return;
  
  const containerWidth = thumbnailNav.value.clientWidth;
  const scrollWidth = thumbnailNav.value.scrollWidth;
  const itemWidth = 80; // 缩略图项宽度 + gap
  
  const pages = Math.ceil(scrollWidth / containerWidth);
  scrollPages.value = Array.from({ length: pages }, (_, i) => i);
};

// 滚动到指定页面
const scrollToPage = (pageIndex: number) => {
  if (!thumbnailNav.value) return;
  
  const containerWidth = thumbnailNav.value.clientWidth;
  const scrollPosition = pageIndex * containerWidth;
  
  thumbnailNav.value.scrollTo({
    left: scrollPosition,
    behavior: 'smooth'
  });
  
  currentScrollPage.value = pageIndex;
};

// 缩略图加载完成处理
const onThumbnailLoad = (event: Event, index: number) => {
  // 缩略图加载完成后重新计算滚动页数
  thumbnailLoadingStates.value.set(index, false);
  nextTick(() => {
    calculateScrollPages();
  });
};

// 缩略图加载错误处理
const onThumbnailError = (event: Event, index: number) => {
  console.warn(`Failed to load thumbnail at index ${index}`);
  thumbnailLoadingStates.value.set(index, false);
};

// 图片加载完成处理
const onImageLoad = (event: Event, index: number) => {
  const img = event.target as HTMLImageElement;
  const dimensions = {
    width: img.naturalWidth,
    height: img.naturalHeight,
    ratio: img.naturalWidth / img.naturalHeight
  };

  imageDimensions.value.set(index, dimensions);
  emit('imageLoad', index, { width: dimensions.width, height: dimensions.height });

  // 如果是当前图片，更新容器尺寸
  if (index === currentIndex.value) {
    updateContainerSize();
  }
};

// 图片加载错误处理
const onImageError = (event: Event, index: number) => {
  console.warn(`Failed to load image at index ${index}`);
};

// 更新容器尺寸
const updateContainerSize = () => {
  const currentImageDimensions = imageDimensions.value.get(currentIndex.value);
  if (!currentImageDimensions) return;

  let { width, height } = currentImageDimensions;
  const { ratio } = currentImageDimensions;

  // 根据aspectRatio prop调整尺寸
  switch (props.aspectRatio) {
    case 'square':
      width = height = Math.min(width, height);
      break;
    case '4:3':
      if (ratio > 4 / 3) {
        width = height * (4 / 3);
      } else {
        height = width * (3 / 4);
      }
      break;
    case '16:9':
      if (ratio > 16 / 9) {
        width = height * (16 / 9);
      } else {
        height = width * (9 / 16);
      }
      break;
    case 'portrait':
      if (ratio > 3 / 4) {
        width = height * (3 / 4);
      } else {
        height = width * (4 / 3);
      }
      break;
    case 'auto':
    default:
      // 保持原始比例
      break;
  }

  // 限制最大尺寸
  if (width > props.maxWidth) {
    height = height * (props.maxWidth / width);
    width = props.maxWidth;
  }
  if (height > props.maxHeight) {
    width = width * (props.maxHeight / height);
    height = props.maxHeight;
  }

  // 设置最小尺寸
  const minWidth = 400;
  const minHeight = 300;
  if (width < minWidth) {
    height = height * (minWidth / width);
    width = minWidth;
  }
  if (height < minHeight) {
    width = width * (minHeight / height);
    height = minHeight;
  }

  containerDimensions.value = { width: Math.round(width), height: Math.round(height) };
};

// 监听当前索引变化，更新容器尺寸和缩略图滚动
watch(currentIndex, () => {
  nextTick(() => {
    updateContainerSize();
    scrollToCurrentThumbnail();
  });
});

// 滚动到当前缩略图
const scrollToCurrentThumbnail = () => {
  if (!thumbnailNav.value || !thumbnailContainer.value) return;
  
  const thumbnailItems = thumbnailNav.value.children;
  if (thumbnailItems.length === 0) return;
  
  const currentThumbnail = thumbnailItems[currentIndex.value] as HTMLElement;
  if (!currentThumbnail) return;
  
  const containerRect = thumbnailContainer.value.getBoundingClientRect();
  const thumbnailRect = currentThumbnail.getBoundingClientRect();
  
  // 计算需要滚动的距离
  const scrollLeft = thumbnailNav.value.scrollLeft;
  const thumbnailLeft = currentThumbnail.offsetLeft;
  const containerWidth = thumbnailContainer.value.clientWidth;
  
  // 确保当前缩略图在可视区域内
  if (thumbnailLeft < scrollLeft || thumbnailLeft + currentThumbnail.offsetWidth > scrollLeft + containerWidth) {
    thumbnailNav.value.scrollTo({
      left: thumbnailLeft - containerWidth / 2 + currentThumbnail.offsetWidth / 2,
      behavior: 'smooth'
    });
  }
};

// 监听图片数组变化
watch(() => props.images, (newImages, oldImages) => {
  console.log('AdaptiveImageCarousel: images changed:', {
    newLength: newImages?.length || 0,
    oldLength: oldImages?.length || 0,
    newImages: newImages
  });
  
  // 如果图片数量增加，重置当前索引到第一张图片
  if (newImages && newImages.length > 0 && (!oldImages || oldImages.length === 0)) {
    currentIndex.value = 0;
  }
  
  // 设置新图片的加载状态
  if (newImages) {
    newImages.forEach((_, index) => {
      thumbnailLoadingStates.value.set(index, true);
    });
  }
  
  nextTick(() => {
    updateContainerSize();
    calculateScrollPages();
  });
}, { deep: true });

// 容器样式
const containerStyle = computed(() => {
  return {
    minHeight: `${containerDimensions.value.height}px`
  };
});

// 获取滑块样式类
const getSlideClass = (index: number) => {
  return {
    'slide-active': index === currentIndex.value,
    'slide-prev': index === currentIndex.value - 1 || (currentIndex.value === 0 && index === props.images.length - 1),
    'slide-next': index === currentIndex.value + 1 || (currentIndex.value === props.images.length - 1 && index === 0),
    'slide-hidden': Math.abs(index - currentIndex.value) > 1 &&
      !(currentIndex.value === 0 && index === props.images.length - 1) &&
      !(currentIndex.value === props.images.length - 1 && index === 0),
    'transitioning': isTransitioning.value
  };
};

// 获取滑块样式
const getSlideStyle = (index: number) => {
  const totalImages = props.images.length;
  let translateX = 0;
  let scale = 0.8;
  let opacity = 0.6;
  let zIndex = 1;

  // 获取当前图片的尺寸信息用于计算偏移
  const currentImageDim = imageDimensions.value.get(currentIndex.value);
  const slideWidth = currentImageDim ? Math.min(currentImageDim.width * 0.6, containerDimensions.value.width * 0.6) : containerDimensions.value.width * 0.6;

  if (index === currentIndex.value) {
    // 当前图片居中，最高层级
    translateX = 0;
    scale = 1;
    opacity = 1;
    zIndex = 10;
  } else if (index === currentIndex.value - 1 || (currentIndex.value === 0 && index === totalImages - 1)) {
    // 左侧图片，较低层级，减少偏移量避免溢出
    translateX = -(slideWidth + 30);
    scale = 0.85;
    opacity = 0.7;
    zIndex = 2; // 降低z-index，让它在活跃图片下面
  } else if (index === currentIndex.value + 1 || (currentIndex.value === totalImages - 1 && index === 0)) {
    // 右侧图片，较低层级，减少偏移量避免溢出
    translateX = slideWidth + 30;
    scale = 0.85;
    opacity = 0.7;
    zIndex = 2; // 降低z-index，让它在活跃图片下面
  } else {
    // 隐藏的图片
    translateX = index < currentIndex.value ? -(slideWidth * 1.5) : (slideWidth * 1.5);
    scale = 0.6;
    opacity = 0;
    zIndex = 1;
  }

  // 动态设置图片容器尺寸
  const imageStyle = {
    transform: `translateX(${translateX}px) scale(${scale})`,
    opacity: opacity,
    zIndex: zIndex,
    width: `${containerDimensions.value.width}px`,
    height: `${containerDimensions.value.height}px`
  };

  return imageStyle;
};

// 轮播图控制方法
const prevImage = () => {
  if (isTransitioning.value || props.images.length === 0) return;

  isTransitioning.value = true;
  const newIndex = currentIndex.value > 0
    ? currentIndex.value - 1
    : props.images.length - 1;

  currentIndex.value = newIndex;

  setTimeout(() => {
    isTransitioning.value = false;
  }, 300);
};

const nextImage = () => {
  if (isTransitioning.value || props.images.length === 0) return;

  isTransitioning.value = true;
  const newIndex = currentIndex.value < props.images.length - 1
    ? currentIndex.value + 1
    : 0;

  currentIndex.value = newIndex;

  setTimeout(() => {
    isTransitioning.value = false;
  }, 300);
};

const goToImage = (index: number) => {
  if (index >= 0 && index < props.images.length && index !== currentIndex.value) {
    if (isTransitioning.value) return;

    isTransitioning.value = true;
    currentIndex.value = index;

    setTimeout(() => {
      isTransitioning.value = false;
    }, 300);
  }
};

// 键盘导航支持
const handleKeydown = (event: KeyboardEvent) => {
  if (props.images.length === 0) return;
  
  switch (event.key) {
    case 'ArrowLeft':
      event.preventDefault();
      prevImage();
      break;
    case 'ArrowRight':
      event.preventDefault();
      nextImage();
      break;
    case 'Home':
      event.preventDefault();
      goToImage(0);
      break;
    case 'End':
      event.preventDefault();
      goToImage(props.images.length - 1);
      break;
  }
};

// 触摸支持
const touchStartX = ref(0);
const touchStartY = ref(0);
const touchEndX = ref(0);
const touchEndY = ref(0);

const handleTouchStart = (event: TouchEvent) => {
  touchStartX.value = event.touches[0].clientX;
  touchStartY.value = event.touches[0].clientY;
};

const handleTouchEnd = (event: TouchEvent) => {
  touchEndX.value = event.changedTouches[0].clientX;
  touchEndY.value = event.changedTouches[0].clientY;
  handleSwipe();
};

const handleSwipe = () => {
  const diffX = touchStartX.value - touchEndX.value;
  const diffY = touchStartY.value - touchEndY.value;
  
  // 确保是水平滑动而不是垂直滑动
  if (Math.abs(diffX) > Math.abs(diffY) && Math.abs(diffX) > 50) {
    if (diffX > 0) {
      // 向左滑动，显示下一张
      nextImage();
    } else {
      // 向右滑动，显示上一张
      prevImage();
    }
  }
};

// 组件挂载后初始化
onMounted(() => {
  nextTick(() => {
    calculateScrollPages();
  });
  
  // 添加键盘事件监听
  document.addEventListener('keydown', handleKeydown);
});

// 组件卸载时清理事件监听
onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown);
});
</script>

<style scoped>
.adaptive-carousel {
  position: relative;
  overflow: hidden;
  /* 防止整体组件溢出 */
}

.carousel-container {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  /* 改回hidden，通过其他方式显示两侧图片 */
  perspective: 1200px;
  transition: min-height 0.5s ease;
  margin: 0 80px;
  /* 使用margin代替padding，避免影响容器大小 */
}

.carousel-main {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: visible;
  /* 让主要内容区域可以溢出显示两侧图片 */
}

.carousel-track {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  transform-style: preserve-3d;
}

.carousel-slide {
  position: absolute;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  transform-origin: center center;
  backface-visibility: hidden;
}

.carousel-slide.transitioning {
  transition-duration: 0.3s;
}

.carousel-slide.slide-active {
  z-index: 10;
  /* 活跃图片最高层级 */
}

.carousel-slide.slide-prev,
.carousel-slide.slide-next {
  z-index: 8;
  /* 两侧图片较高层级，确保圆角正常显示 */
}

.carousel-slide.slide-hidden {
  opacity: 0 !important;
  pointer-events: none;
  z-index: 1;
  /* 隐藏图片最低层级 */
}

.carousel-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 12px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  overflow: hidden;
  /* 确保圆角效果 */
}

.carousel-slide:hover .carousel-image {
  border-color: rgba(0, 0, 0, 0.2);
}

.carousel-slide.slide-active .carousel-image {
  border-radius: 12px;
  border: 2px solid var(--ant-color-primary);
}

/* 确保左右两侧图片的圆角正常显示 */
.carousel-slide.slide-prev .carousel-image,
.carousel-slide.slide-next .carousel-image {
  border-radius: 12px;
  border: 1px solid rgba(0, 0, 0, 0.15);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 箭头导航样式 */
.carousel-arrow {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 60px;
  height: 60px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 20;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.carousel-arrow:hover {
  background: rgba(255, 255, 255, 1);
  transform: translateY(-50%) scale(1.1);
}

.carousel-arrow-left {
  left: -60px;
  /* 放在margin区域内，避免溢出 */
}

.carousel-arrow-right {
  right: -60px;
  /* 放在margin区域内，避免溢出 */
}

.arrow-icon {
  font-size: 24px;
  font-weight: bold;
  color: #333;
}

/* 缩略图容器样式 - 优化版本 */
.thumbnail-container {
  margin-top: 20px;
  padding: 0 20px;
  position: relative;
  background: var(--ant-color-bg-container);
  border-radius: 12px;
  border: 1px solid var(--ant-color-border);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  /* 确保缩略图不会遮挡主图片 */
  z-index: 1;
}

.thumbnail-scroll-container {
  position: relative;
  overflow: hidden;
  border-radius: 8px;
  background: transparent;
  border: none;
}

.thumbnail-nav {
  display: flex;
  gap: 8px;
  padding: 12px;
  overflow-x: auto;
  scroll-behavior: smooth;
  /* 隐藏滚动条但保持功能 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.thumbnail-nav::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

.thumbnail-item {
  flex-shrink: 0;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.thumbnail-wrapper {
  position: relative;
  width: 90px;
  height: 68px;
  border-radius: 10px;
  overflow: hidden;
  border: 3px solid transparent;
  transition: all 0.3s ease;
  background: var(--ant-color-bg-container);
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.12);
  /* 添加渐变边框效果 */
  background: linear-gradient(135deg, var(--ant-color-bg-container) 0%, var(--ant-color-bg-layout) 100%);
}

.thumbnail-wrapper::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 10px;
  padding: 2px;
  background: linear-gradient(135deg, var(--ant-color-primary) 0%, var(--ant-color-primary-hover) 100%);
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.thumbnail-item.active .thumbnail-wrapper::before {
  opacity: 1;
}

.thumbnail-item:hover .thumbnail-wrapper {
  border-color: var(--ant-color-primary-hover);
  transform: scale(1.08);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.18);
}

.thumbnail-item.active .thumbnail-wrapper {
  border-color: var(--ant-color-primary);
  transform: scale(1.15);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.25);
}

.thumbnail-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.3s ease;
}

.thumbnail-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.thumbnail-item:hover .thumbnail-overlay {
  opacity: 1;
}

.thumbnail-index {
  color: white;
  font-size: 11px;
  font-weight: 700;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.6) 100%);
  padding: 3px 8px;
  border-radius: 6px;
  backdrop-filter: blur(4px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 缩略图加载状态样式 */
.thumbnail-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
}

.loading-spinner-small {
  width: 16px;
  height: 16px;
  border: 2px solid var(--ant-color-border);
  border-top: 2px solid var(--ant-color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* 滚动指示器样式 */
.thumbnail-scroll-indicator {
  display: flex;
  justify-content: center;
  margin-top: 8px;
  padding: 0 16px;
}

.scroll-dots {
  display: flex;
  gap: 6px;
  align-items: center;
}

.scroll-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--ant-color-border);
  cursor: pointer;
  transition: all 0.3s ease;
}

.scroll-dot:hover {
  background: var(--ant-color-primary-hover);
  transform: scale(1.2);
}

.scroll-dot.active {
  background: var(--ant-color-primary);
  transform: scale(1.3);
}

/* 响应式样式 */
@media (max-width: 1024px) {
  .carousel-container {
    margin: 0 70px;
    /* 平板屏幕适中margin */
  }
  
  .thumbnail-wrapper {
    width: 80px;
    height: 60px;
  }
}

@media (max-width: 768px) {
  .carousel-container {
    margin: 0 60px;
    /* 中等屏幕减少margin */
  }

  .carousel-arrow {
    width: 50px;
    height: 50px;
  }

  .carousel-arrow-left {
    left: -45px;
    /* 调整到margin区域内 */
  }

  .carousel-arrow-right {
    right: -45px;
    /* 调整到margin区域内 */
  }

  .arrow-icon {
    font-size: 20px;
  }

  .thumbnail-container {
    margin-top: 16px;
    padding: 0 16px;
  }

  .thumbnail-nav {
    gap: 8px;
    padding: 12px;
  }

  .thumbnail-wrapper {
    width: 70px;
    height: 52px;
  }
}

@media (max-width: 480px) {
  .carousel-container {
    margin: 0 40px;
    /* 小屏幕进一步减少margin */
  }

  .carousel-arrow {
    width: 44px;
    height: 44px;
  }

  .carousel-arrow-left {
    left: -32px;
    /* 调整到margin区域内 */
  }

  .carousel-arrow-right {
    right: -32px;
    /* 调整到margin区域内 */
  }

  .arrow-icon {
    font-size: 18px;
  }

  .thumbnail-container {
    margin-top: 12px;
    padding: 0 12px;
  }

  .thumbnail-nav {
    gap: 6px;
    padding: 10px;
  }

  .thumbnail-wrapper {
    width: 60px;
    height: 45px;
  }
}

/* 动画优化 */
@media (prefers-reduced-motion: reduce) {
  .carousel-slide {
    transition: none;
  }
}

/* Loading 效果样式 */
.generating-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.85);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 50;
  border-radius: 12px;
  backdrop-filter: blur(4px);
}

.generating-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.generating-spinner {
  width: 32px;
  height: 32px;
  border: 2px solid var(--ant-color-border);
  border-top: 2px solid var(--ant-color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.generating-text {
  font-size: 14px;
  font-weight: 500;
  color: var(--ant-color-text);
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* 小的loading指示器样式 */
.generating-indicator {
  position: absolute;
  top: 16px;
  right: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  border-radius: 20px;
  backdrop-filter: blur(4px);
  z-index: 30;
}

.generating-spinner-small {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.generating-text-small {
  font-size: 12px;
  font-weight: 500;
  color: white;
}
</style>