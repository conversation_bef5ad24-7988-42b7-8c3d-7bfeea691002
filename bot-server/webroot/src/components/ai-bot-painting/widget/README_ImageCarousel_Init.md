# ImageCarousel 初始化功能实现

## 功能概述

根据需求，我们为 `ImageCarousel.vue` 组件实现了以下功能：

1. **初始化时从 `useAiPaintingStore.current` 解析轮播图数据**
2. **解析 URL 并初始化到轮播图显示**
3. **每次 `AiPaintingStream` 流事件都会更新当前的绘画消息**

## 实现详情

### 1. 初始化函数

```typescript
// 初始化函数：从 useAiPainting.current 解析图片URL
const initializeFromCurrentPainting = () => {
  const currentPainting = useAiPainting.current;
  
  if (currentPainting && currentPainting.imageUrls) {
    // 解析逗号分隔的图片URL
    const urls = currentPainting.imageUrls.split(',')
      .map(url => url.trim())
      .filter(url => url.length > 0);
    
    if (urls.length > 0) {
      internalImages.value = urls;
      hasGeneratedImage.value = true;
      currentIndex.value = 0;
      console.log('从 current 初始化图片:', urls);
    }
  }
  
  // 初始化提示词
  if (currentPainting && currentPainting.prompt) {
    lastPrompt.value = currentPainting.prompt;
  }
  
  // 初始化生成状态
  if (currentPainting && currentPainting.status) {
    isGenerating.value = currentPainting.status === 'running' || currentPainting.status === 'start';
  }
};
```

### 2. 流事件处理

```typescript
// AiPaintingStream 事件处理函数
const handleAiPaintingStream = async (painting: PaintingMessage) => {
  console.log('收到绘画流事件:', painting);
  
  // 更新 store 中的 current 属性
  useAiPainting.current = painting;
  
  // 更新本地状态
  if (painting.prompt) {
    lastPrompt.value = painting.prompt;
  }
  
  // 当开始生成时，设置状态并清空之前的图片
  if (painting.status === 'start' || painting.status === 'running') {
    isGenerating.value = true;
    hasGeneratedImage.value = false;
    internalImages.value = [];
    currentIndex.value = 0;
    console.log('开始生成，清空图片');
  }
  
  // ... 其他流处理逻辑
  
  // 如果直接有图片URL（真实场景），优先使用真实图片
  if (painting.imageUrls && painting.status === 'success') {
    const realImages = painting.imageUrls.split(',').filter(url => url.trim());
    if (realImages.length > 0) {
      internalImages.value = realImages.map(url => url.trim());
      isGenerating.value = false;
      hasGeneratedImage.value = true;
      currentIndex.value = 0;
      console.log('已加载真实图片:', internalImages.value);
    }
  }
};
```

### 3. 生命周期管理

```typescript
// 组件挂载时初始化
onMounted(() => {
  console.log('ImageCarousel 组件挂载，开始初始化');
  
  // 从 current 初始化轮播图
  initializeFromCurrentPainting();
  
  // 监听 AiPaintingStream 事件
  emitter.on(AiPaintingStream, handleAiPaintingStream);
});

// 组件卸载时清理事件监听
onUnmounted(() => {
  console.log('ImageCarousel 组件卸载，清理事件监听');
  emitter.off(AiPaintingStream, handleAiPaintingStream);
});
```

## 主要变更

### 文件：`ImageCarousel.vue`

1. **导入生命周期钩子**：添加了 `onMounted` 和 `onUnmounted`
2. **新增初始化函数**：`initializeFromCurrentPainting()`
3. **重构事件处理**：将原来的内联事件监听改为独立的 `handleAiPaintingStream` 函数
4. **添加生命周期管理**：在组件挂载时初始化和监听事件，卸载时清理

## 功能特性

### ✅ 已实现的功能

- **自动初始化**：组件挂载时自动从 `useAiPaintingStore.current` 读取数据
- **URL 解析**：支持逗号分隔的多个图片 URL 解析
- **状态同步**：自动同步提示词、生成状态等信息
- **事件响应**：响应 `AiPaintingStream` 事件并更新显示
- **状态管理**：更新 store 中的 `current` 属性

### 🔄 工作流程

1. **组件挂载** → 调用 `initializeFromCurrentPainting()` → 解析 `current.imageUrls` → 初始化轮播图
2. **接收流事件** → 更新 `store.current` → 更新本地状态 → 处理图片数据 → 更新轮播图显示
3. **组件卸载** → 清理事件监听器

## 测试

创建了测试页面 `TestImageCarouselInit.vue` 用于验证功能：

- 可以手动设置测试数据到 store
- 可以触发流事件测试
- 实时显示 store 状态
- 验证轮播图响应

## 使用方式

组件现在可以自动工作，无需额外配置：

```vue
<template>
  <ImageCarousel />
</template>
```

组件会自动：
1. 从 `useAiPaintingStore.current` 初始化
2. 监听 `AiPaintingStream` 事件
3. 更新轮播图显示

## 注意事项

- 确保 `useAiPaintingStore.current.imageUrls` 是逗号分隔的 URL 字符串
- 组件会自动处理 URL 的 trim 和过滤
- 支持动态更新，每次流事件都会刷新显示
- 事件监听器会在组件卸载时自动清理，避免内存泄漏
