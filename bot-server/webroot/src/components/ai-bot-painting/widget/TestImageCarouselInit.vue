<template>
  <div class="test-page">
    <h2>ImageCarousel 初始化测试</h2>
    <p class="description">测试从 useAiPaintingStore.current 初始化轮播图功能</p>
    
    <div class="section">
      <h3>控制面板</h3>
      <div class="controls">
        <div class="control-group">
          <label>设置测试图片URL (逗号分隔):</label>
          <textarea 
            v-model="testImageUrls" 
            placeholder="输入图片URL，用逗号分隔"
            rows="3"
          ></textarea>
        </div>
        
        <div class="control-group">
          <label>设置提示词:</label>
          <input 
            v-model="testPrompt" 
            placeholder="输入测试提示词"
            type="text"
          />
        </div>
        
        <div class="control-group">
          <label>设置状态:</label>
          <select v-model="testStatus">
            <option value="success">success</option>
            <option value="running">running</option>
            <option value="start">start</option>
            <option value="error">error</option>
          </select>
        </div>
        
        <div class="button-group">
          <button @click="updateCurrentPainting" class="primary-btn">
            更新 Store Current
          </button>
          <button @click="triggerStreamEvent" class="secondary-btn">
            触发流事件
          </button>
          <button @click="clearCurrent" class="danger-btn">
            清空 Current
          </button>
        </div>
      </div>
    </div>

    <div class="section">
      <h3>当前 Store 状态</h3>
      <div class="status-display">
        <pre>{{ JSON.stringify(currentPaintingState, null, 2) }}</pre>
      </div>
    </div>

    <div class="section">
      <h3>ImageCarousel 组件</h3>
      <div class="carousel-container">
        <ImageCarousel />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useAiPaintingStore } from '../../store/painting';
import { PaintingMessage } from '../../common/model/painting';
import ImageCarousel from '../ImageCarousel.vue';
import emitter from '@/plugins/event';
import { AiPaintingStream } from '@/plugins/evenKey';

// 获取 store
const paintingStore = useAiPaintingStore();

// 测试数据
const testImageUrls = ref('https://raw.githubusercontent.com/vueComponent/ant-design-vue/main/components/carousel/demo/abstract01.jpg,https://raw.githubusercontent.com/vueComponent/ant-design-vue/main/components/carousel/demo/abstract02.jpg,https://raw.githubusercontent.com/vueComponent/ant-design-vue/main/components/carousel/demo/abstract03.jpg');
const testPrompt = ref('测试绘画提示词：美丽的风景画');
const testStatus = ref<'success' | 'running' | 'start' | 'error'>('success');

// 当前 store 状态
const currentPaintingState = computed(() => paintingStore.current);

// 更新 store 中的 current
const updateCurrentPainting = () => {
  const paintingMessage: PaintingMessage = {
    id: 'test-' + Date.now(),
    prompt: testPrompt.value,
    status: testStatus.value,
    imageUrls: testImageUrls.value,
    CreatedAt: new Date().toISOString(),
    UpdatedAt: new Date().toISOString()
  };
  
  paintingStore.current = paintingMessage;
  console.log('已更新 store current:', paintingMessage);
};

// 触发流事件
const triggerStreamEvent = () => {
  const paintingMessage: PaintingMessage = {
    id: 'stream-' + Date.now(),
    prompt: testPrompt.value,
    status: testStatus.value,
    imageUrls: testImageUrls.value,
    CreatedAt: new Date().toISOString(),
    UpdatedAt: new Date().toISOString()
  };
  
  console.log('触发 AiPaintingStream 事件:', paintingMessage);
  emitter.emit(AiPaintingStream, paintingMessage);
};

// 清空 current
const clearCurrent = () => {
  paintingStore.current = {} as PaintingMessage;
  console.log('已清空 store current');
};
</script>

<style scoped>
.test-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  background: #f5f7fa;
  min-height: 100vh;
}

h2 {
  text-align: center;
  color: #2c3e50;
  margin-bottom: 10px;
}

.description {
  text-align: center;
  color: #7f8c8d;
  margin-bottom: 30px;
}

.section {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

h3 {
  color: #34495e;
  margin-bottom: 15px;
  border-bottom: 2px solid #3498db;
  padding-bottom: 5px;
}

.controls {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.control-group label {
  font-weight: 500;
  color: #2c3e50;
}

.control-group input,
.control-group textarea,
.control-group select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
}

.button-group {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

button {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.primary-btn {
  background: #3498db;
  color: white;
}

.secondary-btn {
  background: #95a5a6;
  color: white;
}

.danger-btn {
  background: #e74c3c;
  color: white;
}

button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.status-display {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 15px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  max-height: 300px;
  overflow-y: auto;
}

.carousel-container {
  height: 500px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  overflow: hidden;
}

@media (max-width: 768px) {
  .test-page {
    padding: 15px;
  }
  
  .button-group {
    flex-direction: column;
  }
  
  button {
    width: 100%;
  }
  
  .carousel-container {
    height: 400px;
  }
}
</style>
</template>

<parameter name="add_last_line_newline">true
