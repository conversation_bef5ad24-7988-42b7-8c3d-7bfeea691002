<template>
  <div class="config-panel-content flex flex-col h-full">
    <div class="panel-content flex-1 overflow-auto">
      <!-- 参数设置面板 -->
      <div class="config-section">
        <div class="ai-params">
          <!-- Prompt模板配置 -->
          <div class="param-item">
            <label class="param-label">
              <span>Prompt模板 (promptTemplate)</span>
            </label>
            <a-textarea 
              :value="aiParams.promptTemplate"
              @change="updateParam('promptTemplate', $event.target.value)"
              placeholder="输入预设的提示词模板，例如：一只可爱的小猫在花园里玩耍，阳光明媚，卡通风格"
              :auto-size="{ minRows: 3, maxRows: 6 }"
              show-count
              :maxlength="500" />
          </div>

          <!-- 尺寸配置 -->
          <div class="param-item">
            <label class="param-label">
              <span>尺寸 (size)</span>
            </label>
            <a-select 
              :value="aiParams.size" 
              @change="updateParam('size', $event)"
              placeholder="选择尺寸"
              style="width: 100%"
              :dropdown-match-select-width="false">
              <a-select-option value="1:1 (1024*1024)">1:1 (1024*1024)</a-select-option>
              <a-select-option value="16:9 (1024*576)">16:9 (1024*576)</a-select-option>
              <a-select-option value="9:16 (576*1024)">9:16 (576*1024)</a-select-option>
              <a-select-option value="4:3 (1024*768)">4:3 (1024*768)</a-select-option>
              <a-select-option value="3:4 (768*1024)">3:4 (768*1024)</a-select-option>
            </a-select>
          </div>

          <!-- 推理步数配置 -->
          <div class="param-item">
            <label class="param-label">
              <span>推理步数 (numInferenceSteps)</span>
            </label>
            <div class="slider-container">
              <a-slider 
                :value="aiParams.numInferenceSteps"
                @change="updateParam('numInferenceSteps', $event)"
                :min="1"
                :max="50"
                :marks="stepsMarks"
                :tip-formatter="(value) => `${value} 步`" />
              <a-input-number 
                :value="aiParams.numInferenceSteps"
                @change="updateParam('numInferenceSteps', $event)"
                :min="1"
                :max="50"
                size="small"
                class="param-input-number" />
            </div>
          </div>

          <!-- 引导缩放配置 -->
          <div class="param-item">
            <label class="param-label">
              <span>引导强度 (guidanceScale)</span>
            </label>
            <div class="slider-container">
              <a-slider 
                :value="aiParams.guidanceScale"
                @change="updateParam('guidanceScale', $event)"
                :min="0"
                :max="20"
                :step="0.1"
                :marks="guidanceMarks"
                :tip-formatter="(value) => `${value}`" />
              <a-input-number 
                :value="aiParams.guidanceScale"
                @change="updateParam('guidanceScale', $event)"
                :min="0"
                :max="20"
                :step="0.1"
                :precision="1"
                size="small"
                class="param-input-number" />
            </div>
          </div>

          <!-- 负面提示词 -->
          <div class="param-item">
            <label class="param-label">
              <span>负面提示词 (negativePrompt)</span>
            </label>
            <a-textarea 
              :value="aiParams.negativePrompt"
              @change="updateParam('negativePrompt', $event.target.value)"
              placeholder="描述不希望出现的内容，例如：模糊、低质量、变形..."
              :auto-size="{ minRows: 3, maxRows: 6 }"
              show-count
              :maxlength="500" />
          </div>

          <!-- 自定义宽度 -->
          <div class="param-item">
            <label class="param-label">
              <span>宽度 (width)</span>
            </label>
            <a-input-number 
              :value="aiParams.width"
              @change="updateParam('width', $event)"
              placeholder="输入宽度"
              :min="256"
              :max="2048"
              :step="64"
              style="width: 100%"
              :disabled="!aiParams.useCustomSize"
              :formatter="value => `${value} px`"
              :parser="value => value.replace(' px', '')" />
          </div>

          <!-- 自定义高度 -->
          <div class="param-item">
            <label class="param-label">
              <span>高度 (height)</span>
            </label>
            <a-input-number 
              :value="aiParams.height"
              @change="updateParam('height', $event)"
              placeholder="输入高度"
              :min="256"
              :max="2048"
              :step="64"
              style="width: 100%"
              :disabled="!aiParams.useCustomSize"
              :formatter="value => `${value} px`"
              :parser="value => value.replace(' px', '')" />
          </div>

          <!-- 自定义尺寸开关 -->
          <div class="param-item">
            <div class="param-switch">
              <a-switch 
                :checked="aiParams.useCustomSize"
                @change="updateParam('useCustomSize', $event)"
                size="small" />
              <span class="switch-label">使用自定义尺寸</span>
            </div>
          </div>

          <!-- 生成图片数量配置 -->
          <div class="param-item">
            <label class="param-label">
              <span>生成数量 (Image Count)</span>
            </label>
            <div class="slider-container">
              <a-slider 
                :value="aiParams.imageCount"
                @change="updateParam('imageCount', $event)"
                :min="1"
                :max="8"
                :marks="imageCountMarks"
                :tip-formatter="(value) => `${value} 张`" />
              <a-input-number 
                :value="aiParams.imageCount"
                @change="updateParam('imageCount', $event)"
                :min="1"
                :max="8"
                size="small"
                class="param-input-number"
                :formatter="value => `${value} 张`"
                :parser="value => value.replace(' 张', '')" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { useAiPaintingStore } from '../store/painting';

// AI参数接口定义
interface AiParams {
  name: string;
  promptTemplate: string;
  size: string;
  guidanceScale: number;
  negativePrompt: string;
  width: number;
  height: number;
  useCustomSize: boolean;
  imageCount: number;
  numInferenceSteps: number;
}

// 尺寸映射表
const sizeMap = {
  "1:1 (1024*1024)": { width: 1024, height: 1024 },
  "16:9 (1024*576)": { width: 1024, height: 576 },
  "9:16 (576*1024)": { width: 576, height: 1024 },
  "4:3 (1024*768)": { width: 1024, height: 768 },
  "3:4 (768*1024)": { width: 768, height: 1024 }
};

// Props
interface Props {
  aiParams: AiParams;
  panelWidth: number;
  isResizing: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  panelWidth: 280,
  isResizing: false
});

// Emits - 现在不再需要 emit，因为直接使用状态管理器
const emit = defineEmits<{}>();

// 使用状态管理器
const paintingStore = useAiPaintingStore();

// 滑块标记配置
const guidanceMarks = {
  0: '0',
  5: '5',
  10: '10',
  15: '15',
  20: '20'
};

const imageCountMarks = {
  1: '1',
  2: '2',
  4: '4',
  6: '6',
  8: '8'
};

const stepsMarks = {
  1: '1',
  10: '10',
  20: '20',
  30: '30',
  40: '40',
  50: '50'
};

// 监听AI参数变化，确保组件正确更新
watch(() => props.aiParams, (newParams) => {
  console.log('ConfigPanel: AI参数变化:', newParams);
}, { deep: true });

// Methods
const updateParam = async (key: keyof AiParams, value: any) => {
  try {
    console.log(`ConfigPanel: 更新参数 ${key} = ${value}`);
    
    // 直接更新状态管理器，computed 会自动更新
    const success = await paintingStore.updateParam(key, value);
    if (!success) {
      console.error(`参数 ${key} 更新失败`);
      return;
    }
    
    // 如果更新的是尺寸，同时更新宽度和高度
    if (key === 'size' && sizeMap[value]) {
      const { width, height } = sizeMap[value];
      await paintingStore.updateParam('width', width);
      await paintingStore.updateParam('height', height);
    }
    
    console.log(`ConfigPanel: 参数 ${key} 更新成功`);
  } catch (error) {
    console.error(`更新参数 ${key} 时发生错误:`, error);
  }
};

// 获取所有参数的方法
const getAllParams = () => {
  const allParams = { ...props.aiParams };
  return allParams;
};

// 导出方法供外部调用
defineExpose({
  getAllParams
});

// 监听尺寸变化，自动更新宽高
watch(() => props.aiParams.size, async (newSize) => {
  try {
    if (sizeMap[newSize]) {
      const { width, height } = sizeMap[newSize];
      if (props.aiParams.width !== width) {
        await paintingStore.updateParam('width', width);
      }
      if (props.aiParams.height !== height) {
        await paintingStore.updateParam('height', height);
      }
    }
  } catch (error) {
    console.error('自动更新宽高时发生错误:', error);
  }
}, { immediate: true });
</script>

<style scoped>
/* 配置管理面板 */
.config-panel-content {
  background: transparent;
  height: 100%;
}

/* 面板内容 */
.panel-content {
  padding: 8px;
}

/* 配置管理区域 */
.config-section {
  margin-bottom: 20px;
  padding: 8px;
  border: 1px solid var(--ant-color-border-secondary);
  border-radius: 6px;
  background: var(--ant-color-bg-layout);
}

.section-title {
  font-size: 12px;
  font-weight: 600;
  color: var(--ant-color-text-secondary);
  margin-bottom: 8px;
  padding: 4px 8px;
  text-transform: uppercase;
  background: var(--ant-color-bg-container);
  border-radius: 4px;
  border: 1px solid var(--ant-color-border-secondary);
}

/* AI 参数配置样式 */
.ai-params {
  padding: 12px;
}

.param-item {
  margin-bottom: 20px;
}

.param-label {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 13px;
  font-weight: 500;
  color: var(--ant-color-text);
}



.slider-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.slider-container .ant-slider {
  flex: 1;
}

.param-input-number {
  width: 80px;
  flex-shrink: 0;
}

.param-switch {
  display: flex;
  align-items: center;
  gap: 8px;
}

.switch-label {
  font-size: 13px;
  color: var(--ant-color-text);
}



/* 自定义 Ant Design 组件样式 */
.ai-params .ant-select,
.ai-params .ant-input-number,
.ai-params .ant-input {
  border-radius: 6px;
}

.ai-params .ant-textarea {
  border-radius: 6px;
  resize: vertical;
}

.ai-params .ant-slider-track {
  background: var(--ant-color-primary);
}

.ai-params .ant-slider-handle {
  border-color: var(--ant-color-primary);
}

.ai-params .ant-slider-handle:hover {
  border-color: var(--ant-color-primary-hover);
}

.ai-params .ant-slider-handle:focus {
  border-color: var(--ant-color-primary-hover);
  box-shadow: 0 0 0 5px rgba(24, 144, 255, 0.12);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .slider-container {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }
  
  .param-input-number {
    width: 100%;
  }
  
  .ai-params {
    padding: 8px;
  }
  
  .param-item {
    margin-bottom: 16px;
  }
}

@media (max-width: 1200px) {
  .config-panel {
    min-width: 180px;
  }
}
</style> 