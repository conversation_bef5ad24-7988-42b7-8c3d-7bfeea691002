<template>
  <div class="size-full flex flex-col">
    <!-- 图片轮播区域 -->
    <div class="image-carousel-container flex-1">
      <!-- 删除图片标题区域，避免遮挡图片 -->
      <!-- <div v-if="images.length > 0 && lastPrompt" class="image-title-container">
        <div class="image-title-content">
          <span class="image-title-text">{{ lastPrompt }}</span>
          <span class="image-counter-badge">{{ currentIndex + 1 }} / {{ images.length }}</span>
        </div>
      </div> -->

      <AdaptiveImageCarousel 
        :images="images" 
        :current-index="currentIndex" 
        :show-thumbnails="true" 
        :max-width="700"
        :max-height="300" 
        :aspect-ratio="'auto'" 
        :is-generating="isGenerating"
        @update:current-index="updateCurrentIndex" 
        @image-load="onImageLoad" />
    </div>

    <!-- 删除图片操作按钮区域，避免遮挡图片 -->
    <!-- <div v-if="images.length > 0 && !isGenerating" class="image-actions-bar">
      <div class="actions-container">
        <div class="image-info">
          <span class="image-counter">{{ currentIndex + 1 }} / {{ images.length }}</span>
          <span v-if="lastPrompt" class="prompt-preview">{{ truncatedPrompt }}</span>
        </div>

        <div class="action-buttons">
          <a-button type="default" size="small" @click="handleDownloadCurrentImage" :icon="h('DownloadOutlined')"
            title="下载当前图片">
            下载
          </a-button>

          <a-button type="default" size="small" @click="handleSaveToFiles" :icon="h('FolderAddOutlined')"
            title="保存到文件夹">
            保存
          </a-button>

          <a-dropdown :trigger="['click']">
            <a-button type="default" size="small" :icon="h('MoreOutlined')" title="更多操作">
              更多
            </a-button>
            <template #overlay>
              <a-menu @click="handleMenuClick">
                <a-menu-item key="downloadAll">
                  <DownloadOutlined />
                  下载所有图片
                </a-menu-item>
                <a-menu-item key="saveAll">
                  <FolderAddOutlined />
                  保存所有图片
                </a-menu-item>
                <a-menu-divider />
                <a-menu-item key="clear" danger>
                  <DeleteOutlined />
                  清除结果
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </div>
      </div>
    </div> -->
  </div>
</template>

<script setup lang="ts">
import { computed, h, ref, onMounted, onUnmounted, watch } from 'vue';
import { message, Modal } from 'ant-design-vue';
import {
  DownloadOutlined,
  FolderAddOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue';
import { PaintingMessage } from '../common/model/painting';
import AdaptiveImageCarousel from './AdaptiveImageCarousel.vue';
import emitter from '@/plugins/event';
import { AiPaintingStream } from '@/plugins/evenKey';
import { LLMData, LLMStream } from '../common/stream/llm_stream';
import { useAiPaintingStore } from '../store/painting';
import { AgentAIPaintingItem, AgentItemType, AgentItemStatus } from '../common/model/agent';
import { savePainting } from '../common/paintingRequest';

// 保存图片数据接口
interface SaveImageData {
  folderName: string;
  saveCurrentOnly: boolean;
  saveAllImages: boolean;
  currentIndex: number;
  images: string[];
  prompt: string;
}

// Props - 简化为只接收必要的外部数据
interface Props {
  // 移除外部图片传入，只保留提示词（如果需要的话）
  externalPrompt?: string;
}

const props = withDefaults(defineProps<Props>(), {
  externalPrompt: ''
});

// Emits - 只保留必要的事件
const emit = defineEmits<{
  'save-images': [data: SaveImageData];
  'download-single-image': [index: number];
  'update-has-generated-image': [value: boolean];
}>()

// 内部状态管理
const currentIndex = ref(0);
const lastPrompt = ref('');
const hasGeneratedImage = ref(false);
const useAiPainting = useAiPaintingStore();

// 使用状态管理中的 loading 状态
const isGenerating = computed(() => useAiPainting.ui.loading);

// 存储流数据传递来的 url
const paintingUrls = ref<AgentAIPaintingItem[]>([])

// 截断的提示词显示
const truncatedPrompt = computed(() => {
  if (!lastPrompt.value) return '';
  return lastPrompt.value.length > 30
    ? lastPrompt.value.slice(0, 30) + '...'
    : lastPrompt.value;
});

// 内部图片状态
const internalImages = ref<string[]>([]);

// 初始化函数：从 useAiPainting.current 解析图片URL
const initializeFromCurrentPainting = () => {
  const currentPainting = useAiPainting.current;
  console.log('初始化函数被调用，currentPainting:', currentPainting);

  if (currentPainting && currentPainting.imageUrls) {
    // 解析逗号分隔的图片URL
    const urls = currentPainting.imageUrls.split(',')
      .map(url => url.trim())
      .filter(url => url.length > 0);

    if (urls.length > 0) {
      internalImages.value = urls;
      hasGeneratedImage.value = true;
      currentIndex.value = 0;
      console.log('从 current 初始化图片:', urls);
    }
  }

  // 初始化提示词
  if (currentPainting && currentPainting.prompt) {
    lastPrompt.value = currentPainting.prompt;
    console.log('初始化提示词:', currentPainting.prompt);
  }

  // 初始化生成状态
  if (currentPainting && currentPainting.status) {
    const isLoading = currentPainting.status === AgentItemStatus.Running || currentPainting.status === AgentItemStatus.Start;
    useAiPainting.setLoading(isLoading);
    console.log('初始化生成状态:', currentPainting.status, 'loading:', isLoading);
    
    // 如果状态是完成或成功，确保加载状态为false
    if (currentPainting.status === AgentItemStatus.Finish || currentPainting.status === AgentItemStatus.Success) {
      useAiPainting.setLoading(false);
      console.log('检测到完成状态，设置loading为false');
    }
  }

  // 如果有任何有效数据，确保 hasGeneratedImage 为 true
  if (currentPainting && (
    (currentPainting.imageUrls && currentPainting.imageUrls.trim()) ||
    (currentPainting.prompt && currentPainting.prompt.trim()) ||
    (currentPainting.id && currentPainting.id.trim())
  )) {
    console.log('检测到有效数据，设置 hasGeneratedImage 为 true');
    hasGeneratedImage.value = true;
  }
};

// 使用内部图片数据
const images = computed(() => {
  console.log('images computed 被调用:', {
    isGenerating: isGenerating.value,
    paintingUrlsLength: paintingUrls.value.length,
    internalImagesLength: internalImages.value.length
  });

  // 从 paintingUrls 中提取 data 作为图片链接（优先使用流数据）
  if (paintingUrls.value.length > 0) {
    const urls = paintingUrls.value
      .filter(item => item.data && item.data.trim()) // 过滤掉空数据
      .map(item => item.data)
      .filter(url => url && url.trim()); // 再次过滤确保URL有效
    
    console.log('从paintingUrls提取的图片URLs:', urls);
    if (urls.length > 0) {
      return urls;
    }
  }

  // 使用内部生成的图片
  console.log('使用内部图片:', internalImages.value);
  return internalImages.value;
});

// 图片加载完成回调
const onImageLoad = (index: number, dimensions: { width: number; height: number }) => {
  // 可以在这里处理图片加载完成的逻辑
  console.log(`Image ${index} loaded with dimensions:`, dimensions);
};

// 更新当前索引
const updateCurrentIndex = (index: number) => {
  if (index >= 0 && index < images.value.length) {
    currentIndex.value = index;
  }
};

// 内部图片操作方法
const downloadSingleImage = (index: number) => {
  if (index < 0 || index >= images.value.length) {
    message.warning('图片索引无效');
    return;
  }

  const imageUrl = images.value[index];
  if (!imageUrl) {
    message.warning('图片不存在');
    return;
  }

  // 创建下载链接
  const link = document.createElement('a');
  link.href = imageUrl;

  // 生成文件名
  const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
  const promptPrefix = lastPrompt.value ? lastPrompt.value.slice(0, 20).replace(/[^\w\u4e00-\u9fa5]/g, '_') : 'AI画作';
  link.download = `${promptPrefix}_${index + 1}_${timestamp}.png`;

  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);

  message.success(`图片 ${index + 1} 下载成功`);
};

const saveCurrentImageToFiles = () => {
  // 这里可以实现保存到文件系统的逻辑
  // 目前只是显示成功消息
  message.success(`图片 ${currentIndex.value + 1} 已保存到文件夹`);
};

// 图片操作事件处理方法
const handleDownloadCurrentImage = () => {
  if (images.value.length === 0) {
    message.warning('没有可下载的图像');
    return;
  }
  downloadSingleImage(currentIndex.value);
};

const handleSaveToFiles = () => {
  if (images.value.length === 0) {
    message.warning('没有可保存的图像');
    return;
  }
  saveCurrentImageToFiles();
};

// 下拉菜单点击处理
const handleMenuClick = ({ key }: { key: string }) => {
  switch (key) {
    case 'downloadAll':
      handleDownloadAllImages();
      break;
    case 'saveAll':
      handleSaveAllImages();
      break;
    case 'clear':
      handleClearResult();
      break;
  }
};

// 下载所有图片
const handleDownloadAllImages = () => {
  if (images.value.length === 0) {
    message.warning('没有可下载的图像');
    return;
  }

  // 逐个下载所有图片
  images.value.forEach((_, index) => {
    setTimeout(() => {
      downloadSingleImage(index);
    }, index * 500); // 间隔500ms下载，避免同时下载太多
  });

  message.success(`开始下载 ${images.value.length} 张图片`);
};

// 保存所有图片
const handleSaveAllImages = () => {
  if (images.value.length === 0) {
    message.warning('没有可保存的图像');
    return;
  }

  // 生成默认文件夹名
  const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
  const promptPrefix = lastPrompt.value
    ? lastPrompt.value.slice(0, 20).replace(/[^\w\u4e00-\u9fa5]/g, '_')
    : 'AI画作';
  const defaultFolderName = `${promptPrefix}_${timestamp}`;

  const saveData: SaveImageData = {
    folderName: defaultFolderName,
    saveCurrentOnly: false,
    saveAllImages: true,
    currentIndex: currentIndex.value,
    images: images.value,
    prompt: lastPrompt.value || ''
  };

  emit('save-images', saveData);
};

// 清除结果
const handleClearResult = () => {
  Modal.confirm({
    title: '清除结果',
    content: '确定要清除所有生成的图片吗？此操作不可撤销。',
    okText: '确定清除',
    cancelText: '取消',
    okType: 'danger',
    onOk() {
      clearAllResults();
    }
  });
};

// 清除所有结果的内部方法
const clearAllResults = () => {
  internalImages.value = [];
  paintingUrls.value = []; // 清空绘画URL数据
  currentIndex.value = 0;
  lastPrompt.value = '';
  hasGeneratedImage.value = false;
  useAiPainting.setLoading(false);
  message.info('已清除生成结果');
};

// AiPaintingStream 事件处理函数
const handleAiPaintingStream = async (painting: PaintingMessage) => {
  console.log('收到绘画流事件:', painting);

  // 更新 store 中的 current 属性
  useAiPainting.current = painting;

  // 更新本地状态
  if (painting.prompt) {
    lastPrompt.value = painting.prompt;
  }

  // 当开始生成时，设置状态并清空之前的图片
  if (painting.status === AgentItemStatus.Start || painting.status === AgentItemStatus.Running) {
    useAiPainting.setLoading(true);
    hasGeneratedImage.value = false;
    internalImages.value = [];
    // 清空之前的流数据，开始新的绘画
    paintingUrls.value = [];
    currentIndex.value = 0;
    console.log('开始新的绘画，清空之前的数据');
    
    // 确保状态正确设置
    console.log('设置加载状态为true，当前状态:', {
      loading: useAiPainting.ui.loading,
      hasGeneratedImage: hasGeneratedImage.value,
      currentStatus: painting.status
    });
  }

  // 如果有流数据，监听流的完成
  if (painting.MultipleStream && painting.id) {
    const streamFunction = painting.MultipleStream.get(painting.id);
    if (streamFunction) {
      try {
        console.log('开始监听绘画流...');
        // 执行流请求
        const response = await streamFunction();
        painting.llmStream = new LLMStream(response)

        painting.llmStream.setBegin((data: LLMData) => {
          console.log(data);
        })
        painting.llmStream.setData((data: LLMData) => {
          if (data) {
            try {
              let obj: AgentAIPaintingItem = JSON.parse(data.content)
              console.log('收到流数据:', obj);
              
              // 处理绘画类型的数据
              if (obj.type === AgentItemType.Painting) {
                // 检查是否已经存在相同的ID
                const existingIndex = paintingUrls.value.findIndex(item => item.id === obj.id);
                if (existingIndex === -1) {
                  // 新的绘画项
                  paintingUrls.value.push(obj)
                  console.log('添加新的绘画项:', obj);
                } else {
                  // 更新现有的绘画项
                  paintingUrls.value[existingIndex] = obj;
                  console.log('更新现有绘画项:', obj);
                }
                
                // 如果有图片数据，立即设置hasGeneratedImage为true
                if (obj.data && obj.data.trim()) {
                  hasGeneratedImage.value = true;
                  console.log('设置hasGeneratedImage为true，当前paintingUrls长度:', paintingUrls.value.length);
                  
                  // 强制触发images computed重新计算
                  console.log('当前images值:', images.value);
                }
              }
            } catch (error) {
              console.error('解析流数据失败:', error, data);
            }
          }
        })
        
        painting.llmStream.setError(async (data: LLMData) => {
          console.error('绘画流出错:', data);
          setTimeout(async () => {
            // 设置错误状态
            useAiPainting.setLoading(false);
            message.error('图片生成失败，请重试');
          }, 200)
        })

        painting.llmStream.setFinish((data: LLMData) => {
          setTimeout(async () => {
            // 收集所有成功的图片URL
            const successfulImages = paintingUrls.value
              .filter(item => item.status === AgentItemStatus.Success || item.status === AgentItemStatus.Finish)
              .map(item => item.data)
              .filter(url => url && url.trim());
            
            console.log('流结束，收集到的图片URLs:', successfulImages);
            
            // 更新store中的图片URLs和状态
            if (successfulImages.length > 0) {
              useAiPainting.current.imageUrls = successfulImages.join(',');
              // 确保有图片数据时设置hasGeneratedImage为true
              hasGeneratedImage.value = true;
            }
            
            // 更新current状态为完成
            useAiPainting.current.status = AgentItemStatus.Finish;
            
            // 创建一个符合 PaintingMessage 类型的对象
            const paintingToSave: PaintingMessage = {
              id: useAiPainting.current.id,
              prompt: useAiPainting.current.prompt,
              status: AgentItemStatus.Finish,
              imageUrls: useAiPainting.current.imageUrls,
            };
            end(paintingToSave);
            
            // 流结束时统一设置状态
            useAiPainting.setLoading(false);
            
            console.log('绘画完成，状态已更新:', {
              loading: useAiPainting.ui.loading,
              hasGeneratedImage: hasGeneratedImage.value,
              imageUrls: useAiPainting.current.imageUrls
            });
          }, 200)
        })
        painting.llmStream.setCancel((data: LLMData) => {

        })
        await painting.llmStream.listen()

              } catch (error) {
          console.error('绘画流处理失败:', error);
          useAiPainting.setLoading(false);
        }
    }
  }
};

function end(message: PaintingMessage) {
  savePainting(message);
}

// 组件挂载时初始化
onMounted(() => {
  console.log('ImageCarousel 组件挂载，开始初始化');

  // 从 current 初始化轮播图
  initializeFromCurrentPainting();

  // 监听 AiPaintingStream 事件
  emitter.on(AiPaintingStream, handleAiPaintingStream);
});

// 监听 hasGeneratedImage 变化并 emit 事件
watch(hasGeneratedImage, (newValue) => {
  console.log('hasGeneratedImage 变化:', newValue);
  emit('update-has-generated-image', newValue);
});

// 监听 current 变化，当有数据时立即初始化
watch(() => useAiPainting.current, (newCurrent) => {
  console.log('current 变化:', newCurrent);
  if (newCurrent && (
    (newCurrent.imageUrls && newCurrent.imageUrls.trim()) ||
    (newCurrent.prompt && newCurrent.prompt.trim()) ||
    (newCurrent.id && newCurrent.id.trim())
  )) {
    console.log('检测到 current 有数据，立即初始化');
    initializeFromCurrentPainting();
  }
}, { immediate: true, deep: true });

// 组件卸载时清理事件监听
onUnmounted(() => {
  console.log('ImageCarousel 组件卸载，清理事件监听');
  emitter.off(AiPaintingStream, handleAiPaintingStream);
});

</script>

<style scoped>
/* 主容器样式 */
.image-carousel-container {
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 删除图片标题区域和操作栏样式，避免遮挡图片 */

/* Loading 效果样式 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
  border-radius: 12px;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--ant-color-border);
  border-top: 3px solid var(--ant-color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: 16px;
  font-weight: 500;
  color: var(--ant-color-text);
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* 响应式调整 - 已删除遮挡元素的样式 */
</style>