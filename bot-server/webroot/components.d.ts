/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AAvatar: typeof import('ant-design-vue/es')['Avatar']
    About: typeof import('./src/components/ai-bot-setting/system-setting/About.vue')['default']
    AboutDialog: typeof import('./src/components/system-components/other/AboutDialog.vue')['default']
    AButton: typeof import('ant-design-vue/es')['Button']
    ACard: typeof import('ant-design-vue/es')['Card']
    ACarousel: typeof import('ant-design-vue/es')['Carousel']
    ACheckbox: typeof import('ant-design-vue/es')['Checkbox']
    ACol: typeof import('ant-design-vue/es')['Col']
    ACollapse: typeof import('ant-design-vue/es')['Collapse']
    ACollapsePanel: typeof import('ant-design-vue/es')['CollapsePanel']
    AConfigProvider: typeof import('ant-design-vue/es')['ConfigProvider']
    AdaptiveImageCarousel: typeof import('./src/components/ai-bot-painting/AdaptiveImageCarousel.vue')['default']
    ADatePicker: typeof import('ant-design-vue/es')['DatePicker']
    ADivider: typeof import('ant-design-vue/es')['Divider']
    ADrawer: typeof import('ant-design-vue/es')['Drawer']
    ADropdown: typeof import('ant-design-vue/es')['Dropdown']
    AForm: typeof import('ant-design-vue/es')['Form']
    AFormItem: typeof import('ant-design-vue/es')['FormItem']
    AgentBaseMessage: typeof import('./src/components/ai-bot-message/scene/agent/AgentBaseMessage.vue')['default']
    AgentChatRender: typeof import('./src/components/ai-bot-message/scene/agent/AgentChatRender.vue')['default']
    AgentChatScene: typeof import('./src/components/ai-bot-message/scene/agent/AgentChatScene.vue')['default']
    AgentMcpCall: typeof import('./src/components/ai-bot-message/widget/agent/AgentMcpCall.vue')['default']
    AgentMessage: typeof import('./src/components/ai-bot-message/AgentMessage.vue')['default']
    AgentRender: typeof import('./src/components/ai-bot-message/widget/agent/AgentRender.vue')['default']
    AgentRenderScene: typeof import('./src/components/ai-bot-message/scene/agent/AgentRenderScene.vue')['default']
    AgentSceneBase: typeof import('./src/components/ai-bot-message/scene/agent/AgentSceneBase.vue')['default']
    AgentThinking: typeof import('./src/components/ai-bot-message/widget/agent/AgentThinking.vue')['default']
    AgentTyping: typeof import('./src/components/ai-bot-message/widget/typing/AgentTyping.vue')['default']
    AgentUserScene: typeof import('./src/components/ai-bot-message/scene/agent/AgentUserScene.vue')['default']
    AiBotDebugUI: typeof import('./src/components/ai-bot-debug/AiBotDebugUI.vue')['default']
    AiBotGuide: typeof import('./src/components/ai-bot-guide/AiBotGuide.vue')['default']
    AiBotPlugin: typeof import('./src/components/ai-bot-plugins/AiBotPlugin.vue')['default']
    AiBotSetting: typeof import('./src/components/ai-bot-setting/AiBotSetting.vue')['default']
    AiBotStreamWriter: typeof import('./src/components/ai-bot-message/widget/typing/AiBotStreamWriter.vue')['default']
    AiBotUI: typeof import('./src/components/ai-bot/AiBotUI.vue')['default']
    AiChatMarkDownRender: typeof import('./src/components/ai-bot-message/scene/md-ui/AiChatMarkDownRender.vue')['default']
    AIDisclaimer: typeof import('./src/components/ai-bot-message/AIDisclaimer.vue')['default']
    AiIcon: typeof import('./src/components/ai-bot-widget/AiIcon.vue')['default']
    AiIconButton: typeof import('./src/components/ai-bot-widget/AiIconButton.vue')['default']
    AiMusicUI: typeof import('./src/components/ai-bot-music/AiMusicUI.vue')['default']
    AInput: typeof import('ant-design-vue/es')['Input']
    AInputGroup: typeof import('ant-design-vue/es')['InputGroup']
    AInputNumber: typeof import('ant-design-vue/es')['InputNumber']
    AInputPassword: typeof import('ant-design-vue/es')['InputPassword']
    AInputSearch: typeof import('ant-design-vue/es')['InputSearch']
    AiPaintingUI: typeof import('./src/components/ai-bot-painting/AiPaintingUI.vue')['default']
    AiUserMarkDownRender: typeof import('./src/components/ai-bot-message/scene/md-ui/AiUserMarkDownRender.vue')['default']
    AIVoice: typeof import('./src/components/ai-voice/AIVoice.vue')['default']
    AiWriterMarkDownRender: typeof import('./src/components/ai-bot-message/scene/md-ui/AiWriterMarkDownRender.vue')['default']
    AiWriterMessage: typeof import('./src/components/ai-bot-message/scene/AiWriterMessage.vue')['default']
    AiWriterUI: typeof import('./src/components/ai-bot-writer/AiWriterUI.vue')['default']
    ALayout: typeof import('ant-design-vue/es')['Layout']
    ALayoutContent: typeof import('ant-design-vue/es')['LayoutContent']
    ALayoutHeader: typeof import('ant-design-vue/es')['LayoutHeader']
    ALayoutSider: typeof import('ant-design-vue/es')['LayoutSider']
    AMenu: typeof import('ant-design-vue/es')['Menu']
    AMenuDivider: typeof import('ant-design-vue/es')['MenuDivider']
    AMenuItem: typeof import('ant-design-vue/es')['MenuItem']
    AModal: typeof import('ant-design-vue/es')['Modal']
    ARadioButton: typeof import('ant-design-vue/es')['RadioButton']
    ARadioGroup: typeof import('ant-design-vue/es')['RadioGroup']
    ARow: typeof import('ant-design-vue/es')['Row']
    ASegmented: typeof import('ant-design-vue/es')['Segmented']
    ASelect: typeof import('ant-design-vue/es')['Select']
    ASelectOptGroup: typeof import('ant-design-vue/es')['SelectOptGroup']
    ASelectOption: typeof import('ant-design-vue/es')['SelectOption']
    ASlider: typeof import('ant-design-vue/es')['Slider']
    ASpace: typeof import('ant-design-vue/es')['Space']
    ASpin: typeof import('ant-design-vue/es')['Spin']
    ASwitch: typeof import('ant-design-vue/es')['Switch']
    AsyncTyping: typeof import('./src/components/ai-bot-message/widget/typing/AsyncTyping.vue')['default']
    ATag: typeof import('ant-design-vue/es')['Tag']
    ATextarea: typeof import('ant-design-vue/es')['Textarea']
    ATypographyParagraph: typeof import('ant-design-vue/es')['TypographyParagraph']
    ATypographyText: typeof import('ant-design-vue/es')['TypographyText']
    AUpload: typeof import('ant-design-vue/es')['Upload']
    BackgroundContainer: typeof import('./src/components/desktop/BackgroundContainer.vue')['default']
    BaseMessage: typeof import('./src/components/ai-bot-message/scene/BaseMessage.vue')['default']
    BaseModelSelector: typeof import('./src/components/ai-bot-platform/base/BaseModelSelector.vue')['default']
    BotPluginConfig: typeof import('./src/components/ai-bot-plugins/plugin-config/config-ui/BotPluginConfig.vue')['default']
    BotWriterConfig: typeof import('./src/components/ai-bot-plugins/plugin-config/config-ui/BotWriterConfig.vue')['default']
    ChatEditorPlus: typeof import('./src/components/ai-bot-editor/editor/ChatEditorPlus.vue')['default']
    ChatMessage: typeof import('./src/components/ai-bot-message/ChatMessage.vue')['default']
    ChatNavComponent: typeof import('./src/components/ai-bot-widget/nav/ChatNavComponent.vue')['default']
    ChatScene: typeof import('./src/components/ai-bot-message/scene/ChatScene.vue')['default']
    Check: typeof import('./src/components/ai-bot-guide/Check.vue')['default']
    CKEditorStyle: typeof import('./src/components/ai-bot-editor/editor/CKEditorStyle.vue')['default']
    ClearContext: typeof import('./src/components/ai-bot-editor/editor-tool-bar/clearContext/ClearContext.vue')['default']
    ClearHistory: typeof import('./src/components/ai-bot-editor/editor-tool-bar/clear-history/ClearHistory.vue')['default']
    ClearMessage: typeof import('./src/components/ai-bot-message/ClearMessage.vue')['default']
    CodeBlock: typeof import('./src/components/ai-bot-editor/editor-tool-bar/codeBlock/CodeBlock.vue')['default']
    CollapsedButton: typeof import('./src/components/ai-bot-widget/CollapsedButton.vue')['default']
    CommandItem: typeof import('./src/components/ai-bot-editor/editor/widget/show-commands/CommandItem.vue')['default']
    ConfigPanel: typeof import('./src/components/ai-bot-painting/ConfigPanel.vue')['default']
    ContextCheck: typeof import('./src/components/ai-bot-editor/editor-tool-bar/contextCheck/ContextCheck.vue')['default']
    ConversationItem: typeof import('./src/components/ai-bot-conversation/ConversationItem.vue')['default']
    ConversationOperate: typeof import('./src/components/ai-bot-conversation/ConversationOperate.vue')['default']
    ConversationSearchDropdown: typeof import('./src/components/ai-bot-conversation/ConversationSearchDropdown.vue')['default']
    ConversationUI: typeof import('./src/components/ai-bot-conversation/ConversationUI.vue')['default']
    CreateConversation: typeof import('./src/components/ai-bot-conversation/CreateConversation.vue')['default']
    CreatePlatform: typeof import('./src/components/ai-bot-platform/CreatePlatform.vue')['default']
    CustomConfig: typeof import('./src/components/ai-bot-platform/custom-llm/CustomConfig.vue')['default']
    CustomLLMQuickConfig: typeof import('./src/components/ai-bot-platform/custom-llm/CustomLLMQuickConfig.vue')['default']
    DataSetting: typeof import('./src/components/ai-bot-setting/system-setting/DataSetting.vue')['default']
    DebugNavComponent: typeof import('./src/components/ai-bot-widget/nav/DebugNavComponent.vue')['default']
    DeepSeekConfig: typeof import('./src/components/ai-bot-platform/deep-seek-config/DeepSeekConfig.vue')['default']
    DeepSeekQuickConfig: typeof import('./src/components/ai-bot-platform/deep-seek-config/DeepSeekQuickConfig.vue')['default']
    DescriptionEditing: typeof import('./src/components/ai-bot-widget/DescriptionEditing.vue')['default']
    DesktopHeader: typeof import('./src/components/desktop/DesktopHeader.vue')['default']
    DialogTemplate: typeof import('./src/components/system-components/template/DialogTemplate.vue')['default']
    Document: typeof import('./src/components/ai-bot-writer/Document.vue')['default']
    DocumentItem: typeof import('./src/components/ai-bot-writer/DocumentItem.vue')['default']
    DocumentList: typeof import('./src/components/ai-bot-writer/DocumentList.vue')['default']
    DocumentListHeader: typeof import('./src/components/ai-bot-writer/DocumentListHeader.vue')['default']
    EditImageCard: typeof import('./src/components/ai-bot-editor/editor/widget/EditImageCard.vue')['default']
    Editor: typeof import('./src/components/ai-bot-editor/editor/Editor.vue')['default']
    EditorFull: typeof import('./src/components/ai-bot-editor/editor-tool-bar/editorFull/EditorFull.vue')['default']
    EditorRenderBase: typeof import('./src/components/ai-bot-editor/editor/widget/show-commands/EditorRenderBase.vue')['default']
    ErrMessage: typeof import('./src/components/ai-bot-message/widget/ErrMessage.vue')['default']
    ExamplePlatformQuickConfig: typeof import('./src/components/ai-bot-platform/base/ExamplePlatformQuickConfig.vue')['default']
    FileBlockMD: typeof import('./src/components/ai-bot-editor/editor/widget/file-widget/FileBlockMD.vue')['default']
    FileCardView: typeof import('./src/components/ai-bot-editor/editor/widget/file-widget/FileCardView.vue')['default']
    FileModals: typeof import('./src/components/ai-bot-painting/FileModals.vue')['default']
    FilePathTag: typeof import('./src/components/ai-bot-editor/workspace-index/FilePathTag.vue')['default']
    FunctionRender: typeof import('./src/components/ai-bot-message/widget/md-render/FunctionRender.vue')['default']
    GeneralSettings: typeof import('./src/components/ai-bot-setting/system-setting/GeneralSettings.vue')['default']
    GiteeAIConfig: typeof import('./src/components/ai-bot-platform/gitee-ai-config/GiteeAIConfig.vue')['default']
    GiteeAIQuickConfig: typeof import('./src/components/ai-bot-platform/gitee-ai-config/GiteeAIQuickConfig.vue')['default']
    GuideNavComponent: typeof import('./src/components/ai-bot-widget/nav/GuideNavComponent.vue')['default']
    ImageCarousel: typeof import('./src/components/ai-bot-painting/ImageCarousel.vue')['default']
    ImageEdit: typeof import('./src/components/ai-bot-editor/editor/ImageEdit.vue')['default']
    ImmersiveWindow: typeof import('./src/components/desktop/ImmersiveWindow.vue')['default']
    InstalledTabContent: typeof import('./src/components/ai-bot-mcp/InstalledTabContent.vue')['default']
    JsonEditor: typeof import('./src/components/ai-bot-mcp/JsonEditor.vue')['default']
    KnowledgePluginConfig: typeof import('./src/components/ai-bot-plugins/plugin-config/config-ui/KnowledgePluginConfig.vue')['default']
    LeftDrawerToggleBtn: typeof import('./src/components/desktop/LeftDrawerToggleBtn.vue')['default']
    Loading: typeof import('./src/components/loading/Loading.vue')['default']
    LocationComponent: typeof import('./src/components/ai-bot-widget/LocationComponent.vue')['default']
    MacBar: typeof import('./src/components/desktop/apple/MacBar.vue')['default']
    MacBtnGroup: typeof import('./src/components/desktop/apple/MacBtnGroup.vue')['default']
    MainLayout: typeof import('./src/components/ai-bot-layout/MainLayout.vue')['default']
    MainWorkspace: typeof import('./src/components/ai-bot-painting/MainWorkspace.vue')['default']
    MapSetting: typeof import('./src/components/ai-bot-setting/open-platform-setting/map-setting/MapSetting.vue')['default']
    MarkdownStyle: typeof import('./src/components/ai-bot-message/scene/MarkdownStyle.vue')['default']
    MarketplaceTabContent: typeof import('./src/components/ai-bot-mcp/MarketplaceTabContent.vue')['default']
    McpInstanceRender: typeof import('./src/components/ai-bot-message/widget/md-render/McpInstanceRender.vue')['default']
    McpItem: typeof import('./src/components/ai-bot-editor/editor/widget/mention/McpItem.vue')['default']
    McpMarketplaceList: typeof import('./src/components/ai-bot-mcp/McpMarketplaceList.vue')['default']
    McpPromptDebugComponent: typeof import('./src/components/ai-bot-debug/components/McpPromptDebugComponent.vue')['default']
    McpRender: typeof import('./src/components/ai-bot-message/widget/md-render/McpRender.vue')['default']
    McpServerList: typeof import('./src/components/ai-bot-mcp/McpServerList.vue')['default']
    McpTool: typeof import('./src/components/ai-bot-editor/editor-tool-bar/mcpTool/McpTool.vue')['default']
    McpUI: typeof import('./src/components/ai-bot-mcp/McpUI.vue')['default']
    MentionItem: typeof import('./src/components/ai-bot-editor/editor/widget/mention/MentionItem.vue')['default']
    MenuBar: typeof import('./src/components/ai-bot-layout/MenuBar.vue')['default']
    MenuList: typeof import('./src/components/ai-bot-widget/MenuList.vue')['default']
    MenuListItem: typeof import('./src/components/ai-bot-widget/MenuListItem.vue')['default']
    MessageLoading: typeof import('./src/components/ai-bot-message/widget/MessageLoading.vue')['default']
    MessagePanel: typeof import('./src/components/ai-bot-message/MessagePanel.vue')['default']
    MessageSkeleton: typeof import('./src/components/ai-bot-message/widget/MessageSkeleton.vue')['default']
    MessageToolbar: typeof import('./src/components/ai-bot-message/MessageToolbar.vue')['default']
    ModelCheck: typeof import('./src/components/ai-bot-editor/editor-tool-bar/modelCheck/ModelCheck.vue')['default']
    NetworkToggle: typeof import('./src/components/ai-bot-editor/editor-tool-bar/networkToggle/NetworkToggle.vue')['default']
    NewDocumentButton: typeof import('./src/components/ai-bot-writer/NewDocumentButton.vue')['default']
    NotFound: typeof import('./src/components/system-components/NotFound.vue')['default']
    OllamaConfig: typeof import('./src/components/ai-bot-platform/ollama-config/OllamaConfig.vue')['default']
    OllamaQuickConfig: typeof import('./src/components/ai-bot-platform/ollama-config/OllamaQuickConfig.vue')['default']
    OperateMenu: typeof import('./src/components/ai-bot-widget/OperateMenu.vue')['default']
    PageLayout: typeof import('./src/components/ai-bot-layout/PageLayout.vue')['default']
    PaintingCarousel: typeof import('./src/components/ai-bot-painting/widget/PaintingCarousel.vue')['default']
    PaintingNavComponent: typeof import('./src/components/ai-bot-widget/nav/PaintingNavComponent.vue')['default']
    PaintingStream: typeof import('./src/components/ai-bot-painting/widget/PaintingStream.vue')['default']
    PaintingView: typeof import('./src/components/ai-bot-painting/widget/PaintingView.vue')['default']
    PersonalInformationNavComponent: typeof import('./src/components/ai-bot-widget/nav/PersonalInformationNavComponent.vue')['default']
    PersonalInformationSetting: typeof import('./src/components/ai-bot-setting/system-setting/PersonalInformationSetting.vue')['default']
    PlatformConfig: typeof import('./src/components/ai-bot-platform/PlatformConfig.vue')['default']
    PlatformModelSelector: typeof import('./src/components/ai-bot-widget/nav/PlatformModelSelector.vue')['default']
    PlatformUI: typeof import('./src/components/ai-bot-platform/PlatformUI.vue')['default']
    PluginBaseConfig: typeof import('./src/components/ai-bot-plugins/plugin-config/PluginBaseConfig.vue')['default']
    PluginConfigDrawer: typeof import('./src/components/ai-bot-plugins/plugin-config/PluginConfigDrawer.vue')['default']
    PluginConfigUI: typeof import('./src/components/ai-bot-plugins/plugin-config/PluginConfigUI.vue')['default']
    PluginDetail: typeof import('./src/components/ai-bot-mcp/PluginDetail.vue')['default']
    PluginSettingButton: typeof import('./src/components/ai-bot-widget/PluginSettingButton.vue')['default']
    ProgrammingAssistantPluginConfig: typeof import('./src/components/ai-bot-plugins/plugin-config/config-ui/ProgrammingAssistantPluginConfig.vue')['default']
    PromptInput: typeof import('./src/components/ai-bot-painting/PromptInput.vue')['default']
    QuickPrompts: typeof import('./src/components/ai-bot-message/QuickPrompts.vue')['default']
    RefEdit: typeof import('./src/components/ai-bot-editor/editor/RefEdit.vue')['default']
    RenderScene: typeof import('./src/components/ai-bot-message/scene/RenderScene.vue')['default']
    ResizableConversationPanel: typeof import('./src/components/ai-bot-conversation/ResizableConversationPanel.vue')['default']
    ResizablePanel: typeof import('./src/components/ai-bot-widget/ResizablePanel.vue')['default']
    ResourcePanel: typeof import('./src/components/ai-bot-painting/ResourcePanel.vue')['default']
    RightDrawerToggleBtn: typeof import('./src/components/desktop/RightDrawerToggleBtn.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SceneBase: typeof import('./src/components/ai-bot-message/scene/SceneBase.vue')['default']
    SendBtn: typeof import('./src/components/ai-bot-editor/editor/SendBtn.vue')['default']
    SettingDialog: typeof import('./src/components/system-components/setting/SettingDialog.vue')['default']
    SettingItemList: typeof import('./src/components/system-components/setting/SettingItemList.vue')['default']
    SettingView: typeof import('./src/components/system-components/setting/SettingView.vue')['default']
    SimpleInput: typeof import('./src/components/ai-bot/SimpleInput.vue')['default']
    SourceItem: typeof import('./src/components/ai-bot-editor/editor/widget/show-commands/SourceItem.vue')['default']
    SplitPanel: typeof import('./src/components/ai-bot-widget/SplitPanel.vue')['default']
    StopBtn: typeof import('./src/components/ai-bot-editor/editor/StopBtn.vue')['default']
    SystemSetting: typeof import('./src/components/ai-bot-setting/system-setting/SystemSetting.vue')['default']
    TestCarousel: typeof import('./src/components/ai-bot-painting/widget/TestCarousel.vue')['default']
    TestImageCarouselInit: typeof import('./src/components/ai-bot-painting/widget/TestImageCarouselInit.vue')['default']
    ThemeToggleButton: typeof import('./src/components/ai-bot-widget/ThemeToggleButton.vue')['default']
    ThinkingModeToggle: typeof import('./src/components/ai-bot-editor/editor-tool-bar/thinkingModeToggle/ThinkingModeToggle.vue')['default']
    ThinkingRender: typeof import('./src/components/ai-bot-message/widget/md-render/ThinkingRender.vue')['default']
    Tool: typeof import('./src/components/ai-bot-editor/editor/Tool.vue')['default']
    Tooltip: typeof import('./src/components/ai-bot-editor/editor/widget/show-commands/Tooltip.vue')['default']
    ToolWidget: typeof import('./src/components/ai-bot-editor/editor/tool/ToolWidget.vue')['default']
    TrayMenu: typeof import('./src/components/desktop/TrayMenu.vue')['default']
    TweetUI: typeof import('./src/components/ai-bot-tweet/TweetUI.vue')['default']
    TypewriterPanel: typeof import('./src/components/ai-bot-writer/TypewriterPanel.vue')['default']
    UserScene: typeof import('./src/components/ai-bot-message/scene/UserScene.vue')['default']
    VolcengineConfig: typeof import('./src/components/ai-bot-platform/volcengine-ai-config/VolcengineConfig.vue')['default']
    VolcengineQuickConfig: typeof import('./src/components/ai-bot-platform/volcengine-ai-config/VolcengineQuickConfig.vue')['default']
    Welcome: typeof import('./src/components/ai-bot-guide/Welcome.vue')['default']
    WelcomePanel: typeof import('./src/components/ai-bot/WelcomePanel.vue')['default']
    WindowBar: typeof import('./src/components/desktop/window/WindowBar.vue')['default']
    WindowHeader: typeof import('./src/components/desktop/WindowHeader.vue')['default']
    WindowTool: typeof import('./src/components/ai-bot-widget/WindowTool.vue')['default']
    WorkspaceIndex: typeof import('./src/components/ai-bot-editor/workspace-index/WorkspaceIndex.vue')['default']
    WriterEditor: typeof import('./src/components/ai-bot-writer/WriterEditor.vue')['default']
    WriterNavComponent: typeof import('./src/components/ai-bot-widget/nav/WriterNavComponent.vue')['default']
  }
}
